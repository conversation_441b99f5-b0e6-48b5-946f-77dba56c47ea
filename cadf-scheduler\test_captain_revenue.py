#!/usr/bin/env python3
"""
测试舰长收益计算功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from omni.mongo.mongo_client import init_models
from scheduler.revenue.captain_daily_revenue_scheduler import execute_task


async def test_captain_revenue():
    """测试舰长收益计算"""
    print("开始测试舰长收益计算...")
    
    try:
        # 初始化数据库连接
        await init_models()
        print("数据库连接初始化成功")
        
        # 执行舰长收益计算任务
        await execute_task()
        print("舰长收益计算任务执行完成")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_captain_revenue())
