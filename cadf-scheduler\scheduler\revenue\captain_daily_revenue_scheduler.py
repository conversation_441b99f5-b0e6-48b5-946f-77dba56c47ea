import datetime

from models.models import Captain<PERSON><PERSON>yReve<PERSON><PERSON>, CrewManagement
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理舰长每日收益计算
    """
    pass

# 调度频率：每天早上8点执行调度
@register_scheduler(trigger="cron", hour="8", minute="0")
class CaptainDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行舰长每日收益计算任务
        """
        olog.info("开始计算舰长每日收益...")
        await execute_task()
        olog.info("舰长每日收益计算完成。")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
