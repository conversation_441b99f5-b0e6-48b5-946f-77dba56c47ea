from datetime import datetime, date
from typing import Dict, List

from models.models import Captain<PERSON><PERSON>yRevenue, CrewManagement, UserDailyRevenue
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理舰长每日收益计算
    舰长收益 = 舰员收益 * 0.3
    """
    olog.info("开始执行舰长每日收益计算")

    # 获取昨天的时间戳范围
    today = date.today()
    today_timestamp = int(datetime.combine(today, datetime.min.time()).timestamp())
    yesterday_timestamp = today_timestamp - 86400

    olog.debug(f"计算昨天({yesterday_timestamp})的舰长收益")

    # 查询所有舰长-舰员关系
    crew_relationships = await CrewManagement.find_all().to_list()
    olog.debug(f"查询到 {len(crew_relationships)} 个舰长-舰员关系")

    if not crew_relationships:
        olog.info("未找到任何舰长-舰员关系，跳过计算")
        return

    processed_count = 0
    total_captain_revenue = 0

    # 按舰长分组处理
    captain_crews: Dict[str, List[str]] = {}
    for relationship in crew_relationships:
        captain_id = relationship.captain_user_id
        crew_id = relationship.crew_user_id

        if not captain_id or not crew_id:
            olog.warning(f"舰长-舰员关系数据不完整: captain_id={captain_id}, crew_id={crew_id}")
            continue

        if captain_id not in captain_crews:
            captain_crews[captain_id] = []
        captain_crews[captain_id].append(crew_id)

    olog.debug(f"有效舰长数量: {len(captain_crews)}")

    # 为每个舰长计算收益
    for captain_id, crew_ids in captain_crews.items():
        olog.debug(f"处理舰长 {captain_id}，舰员数量: {len(crew_ids)}")

        # 查询所有舰员昨天的收益
        crew_revenues = await UserDailyRevenue.find(
            UserDailyRevenue.user_id.in_(crew_ids),
            UserDailyRevenue.date == yesterday_timestamp,
            UserDailyRevenue.status == "未结算"
        ).to_list()

        olog.debug(f"舰长 {captain_id} 的舰员昨天收益记录数: {len(crew_revenues)}")

        # 按舰员分组计算每个舰员的总收益
        crew_total_revenues: Dict[str, int] = {}
        crew_revenue_ids: Dict[str, List[str]] = {}

        for revenue in crew_revenues:
            crew_id = revenue.user_id
            revenue_amount = revenue.daily_revenue or 0

            if crew_id not in crew_total_revenues:
                crew_total_revenues[crew_id] = 0
                crew_revenue_ids[crew_id] = []

            crew_total_revenues[crew_id] += revenue_amount
            crew_revenue_ids[crew_id].append(str(revenue.id))

        # 为每个有收益的舰员创建舰长收益记录
        for crew_id, crew_total_revenue in crew_total_revenues.items():
            if crew_total_revenue <= 0:
                continue

            # 计算舰长收益：舰员收益 * 0.3
            captain_revenue = int(crew_total_revenue * 0.3)

            if captain_revenue <= 0:
                continue

            # 检查是否已存在该记录
            existing_captain_revenue = await CaptainDailyRevenue.find_one(
                CaptainDailyRevenue.captain_user_id == captain_id,
                CaptainDailyRevenue.crew_user_id == crew_id,
                CaptainDailyRevenue.date == yesterday_timestamp
            )

            if existing_captain_revenue:
                # 更新现有记录
                await existing_captain_revenue.update({
                    "$set": {
                        "daily_revenue": captain_revenue,
                        "daily_task_revenue_ids": crew_revenue_ids[crew_id],
                        "status": "未结算",
                        "settled_at": None
                    }
                })
                olog.debug(f"更新舰长 {captain_id} 从舰员 {crew_id} 的收益: {captain_revenue}分")
            else:
                # 创建新记录
                new_captain_revenue = CaptainDailyRevenue(
                    captain_user_id=captain_id,
                    crew_user_id=crew_id,
                    date=yesterday_timestamp,
                    daily_revenue=captain_revenue,
                    daily_task_revenue_ids=crew_revenue_ids[crew_id],
                    status="未结算",
                    settled_at=None
                )
                await new_captain_revenue.insert()
                olog.debug(f"创建舰长 {captain_id} 从舰员 {crew_id} 的收益记录: {captain_revenue}分")

            processed_count += 1
            total_captain_revenue += captain_revenue

    olog.info(f"舰长每日收益计算完成，处理了 {processed_count} 条记录，总收益: {total_captain_revenue}分")

# 调度频率：每天早上8点执行调度
@register_scheduler(trigger="cron", hour="8", minute="0")
class CaptainDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行舰长每日收益计算任务
        """
        olog.info("开始计算舰长每日收益...")
        await execute_task()
        olog.info("舰长每日收益计算完成。")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
