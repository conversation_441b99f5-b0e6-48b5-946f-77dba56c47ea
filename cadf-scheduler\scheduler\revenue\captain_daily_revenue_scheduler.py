from datetime import datetime, date
from typing import Dict, List

from models.models import Captain<PERSON><PERSON>yRevenu<PERSON>, CrewManagement, UserDailyRevenue
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """
    处理舰长每日收益计算
    舰长收益 = 舰员收益 * 0.3
    """
    olog.info("开始执行舰长每日收益计算")

    # 获取昨天的时间戳范围
    today = date.today()
    today_timestamp = int(datetime.combine(today, datetime.min.time()).timestamp())
    yesterday_timestamp = today_timestamp - 86400

    olog.debug(f"计算昨天({yesterday_timestamp})的舰长收益")

    # 查询所有舰长-舰员关系
    crew_relationships = await CrewManagement.find_all().to_list()
    olog.debug(f"查询到 {len(crew_relationships)} 个舰长-舰员关系")

    if not crew_relationships:
        olog.info("未找到任何舰长-舰员关系，跳过计算")
        return

    processed_count = 0
    total_captain_revenue = 0

    # 按舰长分组处理，同时收集所有舰员ID
    captain_crews: Dict[str, List[str]] = {}
    all_crew_ids = set()

    for relationship in crew_relationships:
        captain_id = relationship.captain_user_id
        crew_id = relationship.crew_user_id

        if not captain_id or not crew_id:
            olog.warning(f"舰长-舰员关系数据不完整: captain_id={captain_id}, crew_id={crew_id}")
            continue

        if captain_id not in captain_crews:
            captain_crews[captain_id] = []
        captain_crews[captain_id].append(crew_id)
        all_crew_ids.add(crew_id)

    olog.debug(f"有效舰长数量: {len(captain_crews)}, 总舰员数量: {len(all_crew_ids)}")

    if not all_crew_ids:
        olog.info("未找到有效的舰员ID，跳过计算")
        return

    # 一次性查询所有舰员昨天的收益（避免N+1查询）
    all_crew_revenues = await UserDailyRevenue.find(
        UserDailyRevenue.user_id.in_(list(all_crew_ids)),
        UserDailyRevenue.date == yesterday_timestamp,
        UserDailyRevenue.status == "未结算"
    ).to_list()

    olog.debug(f"查询到所有舰员昨天收益记录数: {len(all_crew_revenues)}")

    # 按舰员ID分组收益数据
    crew_revenues_map: Dict[str, List[UserDailyRevenue]] = {}
    for revenue in all_crew_revenues:
        crew_id = revenue.user_id
        if crew_id not in crew_revenues_map:
            crew_revenues_map[crew_id] = []
        crew_revenues_map[crew_id].append(revenue)

    # 一次性查询所有可能存在的舰长收益记录（避免重复查询）
    existing_captain_revenues = await CaptainDailyRevenue.find(
        CaptainDailyRevenue.date == yesterday_timestamp
    ).to_list()

    # 建立现有记录的索引
    existing_records_map: Dict[tuple, CaptainDailyRevenue] = {}
    for record in existing_captain_revenues:
        key = (record.captain_user_id, record.crew_user_id)
        existing_records_map[key] = record

    olog.debug(f"查询到现有舰长收益记录数: {len(existing_captain_revenues)}")

    # 准备批量操作的数据
    records_to_insert = []
    records_to_update = []

    # 为每个舰长计算收益
    for captain_id, crew_ids in captain_crews.items():
        olog.debug(f"处理舰长 {captain_id}，舰员数量: {len(crew_ids)}")

        # 计算该舰长下每个舰员的总收益
        for crew_id in crew_ids:
            crew_revenues = crew_revenues_map.get(crew_id, [])

            if not crew_revenues:
                continue

            # 计算舰员总收益和收集收益ID
            crew_total_revenue = 0
            crew_revenue_ids = []

            for revenue in crew_revenues:
                crew_total_revenue += revenue.daily_revenue or 0
                crew_revenue_ids.append(str(revenue.id))

            if crew_total_revenue <= 0:
                continue

            # 计算舰长收益：舰员收益 * 0.3
            captain_revenue = int(crew_total_revenue * 0.3)

            if captain_revenue <= 0:
                continue

            # 检查是否已存在该记录
            record_key = (captain_id, crew_id)
            existing_record = existing_records_map.get(record_key)

            if existing_record:
                # 准备更新数据
                records_to_update.append({
                    "record": existing_record,
                    "daily_revenue": captain_revenue,
                    "daily_task_revenue_ids": crew_revenue_ids
                })
            else:
                # 准备插入数据
                new_record = CaptainDailyRevenue(
                    captain_user_id=captain_id,
                    crew_user_id=crew_id,
                    date=yesterday_timestamp,
                    daily_revenue=captain_revenue,
                    daily_task_revenue_ids=crew_revenue_ids,
                    status="未结算",
                    settled_at=None
                )
                records_to_insert.append(new_record)

            processed_count += 1
            total_captain_revenue += captain_revenue

    # 批量插入新记录
    if records_to_insert:
        await CaptainDailyRevenue.insert_many(records_to_insert)
        olog.info(f"批量插入 {len(records_to_insert)} 条新的舰长收益记录")

    # 批量更新现有记录
    if records_to_update:
        for update_data in records_to_update:
            record = update_data["record"]
            await record.update({
                "$set": {
                    "daily_revenue": update_data["daily_revenue"],
                    "daily_task_revenue_ids": update_data["daily_task_revenue_ids"],
                    "status": "未结算",
                    "settled_at": None
                }
            })
        olog.info(f"批量更新 {len(records_to_update)} 条现有的舰长收益记录")

    olog.info(f"舰长每日收益计算完成，处理了 {processed_count} 条记录，总收益: {total_captain_revenue}分")

# 调度频率：每天早上8点执行调度
@register_scheduler(trigger="cron", hour="8", minute="0")
class CaptainDailyRevenueScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """
        执行舰长每日收益计算任务
        """
        olog.info("开始计算舰长每日收益...")
        await execute_task()
        olog.info("舰长每日收益计算完成。")


if __name__ == "__main__":
    import asyncio


    async def main():
        await init_models()
        await execute_task()


    asyncio.run(main())
