<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9aad99f0-ece8-4628-b7a5-7032d1790b83" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../cadf-consumer-spider-xhs/consumers/xhs_account_traffic_metrics_spider_consumer.py" beforeDir="false" afterPath="$PROJECT_DIR$/../cadf-consumer-spider-xhs/consumers/xhs_account_traffic_metrics_spider_consumer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scheduler/revenue/captain_daily_revenue_scheduler.py" beforeDir="false" afterPath="$PROJECT_DIR$/scheduler/revenue/captain_daily_revenue_scheduler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scheduler/revenue/daily_task_revenue_scheduler.py" beforeDir="false" afterPath="$PROJECT_DIR$/scheduler/revenue/user_daily_revenue_scheduler.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test/__init__.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test/test_account_traffic_spider_scheduler.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test/test_balance_update_scheduler.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/test/test_product_traffic_metrics_scheduler.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../common-module/models/models.py" beforeDir="false" afterPath="$PROJECT_DIR$/../common-module/models/models.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2ziTi3n5KIgBc6RWJSBKWfSLIDh" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.account_traffic_metrics_snapshot_scheduler.executor": "Run",
    "Python.balance_update_scheduler.executor": "Run",
    "Python.retry_ai_generation_scheduler.executor": "Run",
    "Python.start_all.executor": "Run",
    "Python.test_balance_update_scheduler.executor": "Run",
    "Python.test_execute_task.executor": "Run",
    "Python.test_product_traffic_execute.executor": "Run",
    "Python.test_product_traffic_metrics_scheduler (1).executor": "Run",
    "Python.user_daily_revenue_scheduler.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/project/cyber-ad-factory/common-module",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.intellij.pycharm.community.ide.impl.configuration.PythonContentEntriesConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\test" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\scheduler\traffic" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\scheduler\revenue" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\scheduler\other" />
      <recent name="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\scheduler\spider" />
    </key>
  </component>
  <component name="RunManager" selected="Python.user_daily_revenue_scheduler">
    <configuration name="account_traffic_metrics_snapshot_scheduler" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-scheduler" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/scheduler/traffic" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/scheduler/traffic/account_traffic_metrics_snapshot_scheduler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_balance_update_scheduler" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-scheduler" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test/test_balance_update_scheduler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_product_traffic_metrics_scheduler (1)" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-scheduler" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test/test_product_traffic_metrics_scheduler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_product_traffic_metrics_scheduler" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-scheduler" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/test" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="C:\Users\<USER>\Desktop\project\cyber-ad-factory\cadf-scheduler\test\test_product_traffic_metrics_scheduler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="user_daily_revenue_scheduler" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="cadf-scheduler" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/scheduler/revenue" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/scheduler/revenue/user_daily_revenue_scheduler.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.user_daily_revenue_scheduler" />
        <item itemvalue="Python.account_traffic_metrics_snapshot_scheduler" />
        <item itemvalue="Python.test_balance_update_scheduler" />
        <item itemvalue="Python.test_product_traffic_metrics_scheduler (1)" />
        <item itemvalue="Python.test_product_traffic_metrics_scheduler" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-PY-243.26574.90" />
        <option value="bundled-python-sdk-c1fac28bca04-4df51de95216-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.26574.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="9aad99f0-ece8-4628-b7a5-7032d1790b83" name="更改" comment="" />
      <created>1752213018091</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752213018091</updated>
      <workItem from="1752213019142" duration="12080000" />
      <workItem from="1752638050007" duration="10025000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/cadf_scheduler$test_product_traffic_execute.coverage" NAME="test_product_traffic_execute 覆盖结果" MODIFIED="1752645884822" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$balance_update_scheduler.coverage" NAME="balance_update_scheduler 覆盖结果" MODIFIED="1752230468434" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scheduler/other" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$start_all.coverage" NAME="start_all 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$account_traffic_metrics_snapshot_scheduler.coverage" NAME="account_traffic_metrics_snapshot_scheduler 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scheduler/traffic" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$retry_ai_generation_scheduler.coverage" NAME="retry_ai_generation_scheduler 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scheduler" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$test_balance_update_scheduler.coverage" NAME="test_balance_update_scheduler 覆盖结果" MODIFIED="*************" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$test_execute_task.coverage" NAME="test_execute_task 覆盖结果" MODIFIED="1752643488959" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$user_daily_revenue_scheduler.coverage" NAME="user_daily_revenue_scheduler 覆盖结果" MODIFIED="1752654699349" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scheduler/revenue" />
    <SUITE FILE_PATH="coverage/cadf_scheduler$test_product_traffic_metrics_scheduler__1_.coverage" NAME="test_product_traffic_metrics_scheduler (1) 覆盖结果" MODIFIED="1752646187431" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/test" />
  </component>
</project>