import time
from typing import List

from models.models import AccountTrafficMetrics, AccountTrafficMetricsSnapshot
from omni.log.log import olog
from omni.mongo.mongo_client import init_models
from omni.scheduler.base_schedule import BaseScheduler
from omni.scheduler.schedule_register import register_scheduler


async def execute_task() -> None:
    """创建 AccountTrafficMetrics 7天内数据的快照"""
    olog.info("开始创建 AccountTrafficMetrics 快照（仅7天内数据）")

    # 获取当前时间戳
    snapshot_time: int = int(time.time())

    # 计算7天前的时间戳
    seven_days_ago: int = snapshot_time - (7 * 24 * 60 * 60)

    # 查询7天内的 AccountTrafficMetrics 数据（基于 crawled_at 字段）
    recent_account_metrics: List[AccountTrafficMetrics] = await AccountTrafficMetrics.find(
        AccountTrafficMetrics.crawled_at >= seven_days_ago
    ).to_list()
    olog.debug(f"查询到 {len(recent_account_metrics)} 条7天内的 AccountTrafficMetrics 数据")

    # 批量创建快照记录
    snapshot_records: List[AccountTrafficMetricsSnapshot] = []

    for metric in recent_account_metrics:
        # 创建快照记录，复制所有字段并添加快照时间戳
        snapshot_record = AccountTrafficMetricsSnapshot(
            account_id=metric.account_id,
            product_id=metric.product_id,
            ai_generated_material_id=metric.ai_generated_material_id,
            promotion_task_detail_id=metric.promotion_task_detail_id,
            platform=metric.platform,
            crawled_at=metric.crawled_at,
            snapshot_at=snapshot_time,
            title=metric.title,
            publish_time=metric.publish_time,
            view_count=metric.view_count,
            like_count=metric.like_count,
            comment_count=metric.comment_count,
            favorite_count=metric.favorite_count,
            share_count=metric.share_count
        )
        snapshot_records.append(snapshot_record)

    # 批量插入快照记录
    if snapshot_records:
        await AccountTrafficMetricsSnapshot.insert_many(snapshot_records)
        olog.info(f"成功创建 {len(snapshot_records)} 条 AccountTrafficMetrics 快照记录")
    else:
        olog.info("没有找到7天内的 AccountTrafficMetrics 数据，跳过快照创建")


# 调度频率：每天晚上11点执行一次
@register_scheduler(trigger="cron", hour="23", minute="0")
class AccountTrafficMetricsSnapshotScheduler(BaseScheduler):
    async def run_task(self) -> None:
        """创建 AccountTrafficMetrics 快照，每天晚上11点执行一次"""
        await execute_task()


if __name__ == "__main__":
    import asyncio


    async def main() -> None:
        await init_models()
        await execute_task()


    asyncio.run(main())
